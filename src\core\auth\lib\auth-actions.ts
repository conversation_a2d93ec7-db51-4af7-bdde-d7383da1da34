"use server";

import { COOKIE_NAMES } from "@/config/cookies/name";
import { createCookie } from "@/shared/lib/cookies/crud/create";
import { getCookie } from "@/shared/lib/cookies/crud/get";
import { removeCookie } from "@/shared/lib/cookies/crud/remove";
import { getJWTMaxAge } from "@/shared/lib/jwt/max-age";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { cookies } from "next/headers";

/**
 * Interface para resultado de validação de token
 */
interface ITokenValidationResult {
	isValid: boolean;
	error?: string;
}

/**
 * Valida se o token JWT é válido
 */
const validateJWTToken = (token: string): ITokenValidationResult => {
	try {
		if (!token || typeof token !== "string") {
			return { isValid: false, error: "Token inválido ou ausente" };
		}

		const maxAge = getJWTMaxAge(token);
		if (maxAge === null || maxAge <= 0) {
			return { isValid: false, error: "Token expirado ou inválido" };
		}

		return { isValid: true };
	} catch (error) {
		return {
			isValid: false,
			error: `Erro na validação do token: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
		};
	}
};

/**
 * Remove todos os tokens de autenticação
 */
export const clearAuthTokens = async (): Promise<ApiResponse<{ message: string }>> => {
	try {
		const result = await removeCookie({ name: COOKIE_NAMES.ACCESS_TOKEN });
		if (!result.success) {
			return {
				success: false,
				data: { message: "Erro ao remover token de acesso" },
				status: 500,
			};
		}

		// Remove refresh token se existir
		const refreshResult = await removeCookie({ name: COOKIE_NAMES.REFRESH_TOKEN });
		if (!refreshResult.success) {
			console.warn("Erro ao remover refresh token:", refreshResult.data.message);
		}

		return {
			success: true,
			data: { message: "Tokens removidos com sucesso" },
			status: 200,
		};
	} catch (error) {
		return {
			success: false,
			data: {
				message: `Erro interno ao remover tokens: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
			},
			status: 500,
		};
	}
};

/**
 * Obtém o token de acesso atual
 */
export const getAuthToken = async (): Promise<string | null> => {
	try {
		// const { success, value } = await getCookie({
		// 	name: COOKIE_NAMES.ACCESS_TOKEN,
		// });

		// console.log("Obtendo token de acesso:", { success, value });

		// if (!success || !value) {
		// 	return null;
		// }

		// // Valida se o token ainda é válido
		// const validation = validateJWTToken(value);
		// if (!validation.isValid) {
		// 	// Remove token inválido automaticamente
		// 	await clearAuthTokens();
		// 	return null;
		// }

				const cookieStore = await cookies();
				
		const cookieName = "access_token"; // Use o nome do cookie diretamente
		const cookie = cookieStore.get(cookieName);

		console.log("Obtendo token de acesso:", { cookieName, cookie });

		return cookie?.value ?? null;
	} catch (error) {
		console.error("Erro ao obter token de acesso:", error);
		return null;
	}
};

/**
 * Armazena tokens de autenticação como cookies seguros
 * Implementa Single Responsibility Principle (SRP) e Open/Closed Principle (OCP)
 */
export const setAuthTokens = async (accessToken: string, refreshToken?: string): Promise<ApiResponse<boolean>> => {
	try {
		// Valida token de acesso
		const accessTokenValidation = validateJWTToken(accessToken);
		if (!accessTokenValidation.isValid) {
			return {
				success: false,
				data: {
					message: accessTokenValidation.error || "Token de acesso inválido",
				},
				status: 400,
			};
		}

		// Valida refresh token se fornecido
		if (refreshToken) {
			const refreshTokenValidation = validateJWTToken(refreshToken);
			if (!refreshTokenValidation.isValid) {
				console.warn("Refresh token inválido fornecido:", refreshTokenValidation.error);
			}
		}

		// Função auxiliar para criar cookie com configurações seguras
		const createSecureCookie = async (name: string, token: string) => {
			const maxAge = getJWTMaxAge(token) ?? 3600; // Fallback para 1 hora

			return createCookie({
				name,
				value: token,
				options: {
					secure: true,
					sameSite: "strict",
					maxAge,
					httpOnly: true,
					path: "/",
				},
			});
		};

		// Armazena access token
		const accessTokenResult = await createSecureCookie(COOKIE_NAMES.ACCESS_TOKEN, accessToken);
		if (!accessTokenResult.success) {
			return {
				success: false,
				data: {
					message: "Erro ao armazenar token de acesso",
				},
				status: 500,
			};
		}

		// Armazena refresh token se fornecido
		if (refreshToken) {
			const refreshTokenResult = await createSecureCookie(COOKIE_NAMES.REFRESH_TOKEN, refreshToken);
			if (!refreshTokenResult.success) {
				console.warn("Erro ao armazenar refresh token:", refreshTokenResult.data.message);
				// Não falha o processo se refresh token falhar
			}
		}

		return {
			success: true,
			data: true,
			status: 200,
		};
	} catch (error) {
		console.error("Erro ao armazenar tokens de autenticação:", error);
		return {
			success: false,
			data: {
				message: `Erro interno ao armazenar tokens: ${error instanceof Error ? error.message : "Erro desconhecido"}`,
			},
			status: 500,
		};
	}
};

/**
 * Verifica se o usuário está autenticado
 */
export const isAuthenticated = async (): Promise<boolean> => {
	const token = await getAuthToken();
	return token !== null;
};
