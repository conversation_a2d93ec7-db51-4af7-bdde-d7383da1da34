// import { getAuthToken } from "@/core/auth/lib/auth-actions";
// import { AxiosInstance } from "axios";

// export const authHeadersInterceptor = async (instance: AxiosInstance) => {
// 	instance.interceptors.request.use(async config => {
// 		const token = await getAuthToken();
// 		if (token && config.headers) {
// 			config.headers.Authorization = `Bearer ${token}`;
// 		}
// 		return config;
// 	});
// };
