import { NextRequest, NextResponse } from "next/server";
import { extractLoginParams, fetchBackendLoginUrl, isRedirectResponse, isValidRedirectPath } from "@/core/auth/lib/login-utils";
import { API_ROUTE } from "@/config/api/instance";

export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const currentUrl = new URL(request.url);
		if (API_ROUTE?.includes(currentUrl.origin)) {
			console.error("❌ LOOP DETECTADO: API_ROUTE aponta para o próprio frontend!");
			return NextResponse.json(
				{
					error: "configuration_error",
					message: "Configuração inválida: API_ROUTE está apontando para o próprio frontend. Configure para apontar para o backend.",
				},
				{ status: 500 }
			);
		}

		const { redirectPath } = extractLoginParams(new URL(request.url));

		if (!isValidRedirectPath(redirectPath)) {
			console.warn(`⚠️ Path de redirecionamento inválido: ${redirectPath}`);
			return NextResponse.json({ error: "invalid_redirect", message: "Path de redirecionamento inválido" }, { status: 400 });
		}

		console.log(`🔄 Iniciando login - Redirect para: ${redirectPath}`);
		console.log(`🌐 Chamando backend: ${API_ROUTE}/auth/login`);

		const backendResponse = await fetchBackendLoginUrl(redirectPath);

		if (isRedirectResponse(backendResponse)) {
			const location = backendResponse.location;
			if (!location) {
				console.error("❌ URL de redirecionamento não encontrada na resposta do backend");
				throw new Error("URL de redirecionamento não encontrada");
			}

			console.log(`✅ Redirecionando para Keycloak: ${location}`);
			return NextResponse.redirect(location, 302);
		}

		console.error("❌ Resposta inválida do backend:", backendResponse.errorText);
		return NextResponse.json({ error: "backend_error", message: "Erro na comunicação com o servidor de autenticação" }, { status: 500 });
	} catch (error) {
		console.error("❌ Erro no redirecionamento para Keycloak:", error);
		return NextResponse.json(
			{
				error: "keycloak_error",
				message: "Erro interno ao processar redirecionamento para autenticação",
				details: error instanceof Error ? error.message : "Erro desconhecido",
			},
			{ status: 500 }
		);
	}
}
